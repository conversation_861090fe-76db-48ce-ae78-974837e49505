---
import type { HTMLAttributes } from 'astro/types'

type Props = HTMLAttributes<'header'>

const { ...attrs } = Astro.props

const { title, author, socials } = Astro.locals.config
---

<header un-lg="grow-1 justify-between items-start" un-flex="~ col gap-2.5" class="text-center" {...attrs}>
  <hgroup
    un-hover:lg="bg-foreground color-background pt-3.75 pb-8.75 "
    un-lg=" write-vertical-right items-start px-2.5 pb-12 b-l-2px b-l-foreground-solid text-left"
    un-flex="~ col gap-2.5"
    class="cursor-pointer transition-[padding,background] duration-800 ease-in-out"
  >
    <a class="normal" href="/">
      <h3 class="text-5 font-extrabold font-serif">{author}</h3>
      <h1 class="text-8 font-extrabold font-serif">{title}</h1>
    </a>
  </hgroup>

  <nav class="text-center font-bold" un-flex="~ col gap-2">
    <ul un-flex="~ row gap-1 justify-center">
      {
        socials.map((soc) => (
          <li>
            <a href={soc.href} target="_blank" title={soc.name} class={`icon i-mdi-${soc.name}`} />
          </li>
        ))
      }
    </ul>
  </nav>
</header>
