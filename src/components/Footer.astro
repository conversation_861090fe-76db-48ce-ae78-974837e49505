---
import type { HTMLAttributes } from 'astro/types'

type Props = HTMLAttributes<'footer'>

const { ...attrs } = Astro.props
const { socials } = Astro.locals.config
---

<footer {...attrs}>
  <!-- 社交媒体图标 -->
  <nav class="text-center pb-8">
    <ul un-flex="~ row gap-1 justify-center">
      {
        socials.map((soc) => (
          <li>
            <a href={soc.href} target="_blank" title={soc.name} class={`icon i-mdi-${soc.name}`} />
          </li>
        ))
      }
    </ul>
  </nav>
</footer>
