---
import type { HTMLAttributes } from 'astro/types'

type Props = HTMLAttributes<'footer'>

const { ...attrs } = Astro.props
const { navs } = Astro.locals.config
const { translate: t } = Astro.locals
---

<footer {...attrs}>
  <!-- 导航链接 -->
  <nav class="text-center">
    <ul un-flex="~ row gap-4 justify-center flex-wrap" class="text-sm">
      {
        navs.map((nav) => (
          <li>
            <a href={nav.href} class="text-gray-400 hover:text-white transition-colors">
              {t(nav.name)}
            </a>
          </li>
        ))
      }
    </ul>
  </nav>
</footer>
